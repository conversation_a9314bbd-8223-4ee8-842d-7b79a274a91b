/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    gpio.h
  * @brief   This file contains all the function prototypes for
  *          the gpio.c file
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __GPIO_H__
#define __GPIO_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* USER CODE BEGIN Includes */

#define LED_OFF        HAL_GPIO_WritePin(GPIOC, LED_Pin, GPIO_PIN_RESET)    // LED1�رգ��͵�ƽ��Ч
#define LED_ON         HAL_GPIO_WritePin(GPIOC, LED_Pin, GPIO_PIN_SET)      // LED1�򿪣��ߵ�ƽ��Ч
#define LED_TOGGLE     HAL_GPIO_TogglePin(GPIOC, LED_Pin)                   // LED1״̬��ת

#define RF_PWR_OFF      HAL_GPIO_WritePin(GPIOB, RF_PWR_Pin, GPIO_PIN_RESET)     
#define RF_PWR_ON       HAL_GPIO_WritePin(GPIOB, RF_PWR_Pin, GPIO_PIN_SET)       
  
#define CAT1_PWR_OFF      HAL_GPIO_WritePin(GPIOB, CAT1_PWR_Pin, GPIO_PIN_RESET)     
#define CAT1_PWR_ON       HAL_GPIO_WritePin(GPIOB, CAT1_PWR_Pin, GPIO_PIN_SET)  
/* USER CODE END Includes */

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

void MX_GPIO_Init(void);

/* USER CODE BEGIN Prototypes */

/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif
#endif /*__ GPIO_H__ */

